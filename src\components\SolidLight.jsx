import { useState, useEffect, useRef } from 'react'
import { useNavigate } from 'react-router-dom'
import './SolidLight.css'

const SolidLight = () => {
  const navigate = useNavigate()
  const [showControls, setShowControls] = useState(false)
  const [currentColor, setCurrentColor] = useState('#ffffff')
  const [brightness, setBrightness] = useState(80)
  const [isTransitioning, setIsTransitioning] = useState(false)
  const hideTimeoutRef = useRef(null)
  const controlsRef = useRef(null)
  const [showScrollHint, setShowScrollHint] = useState(false)
  const [scrollProgress, setScrollProgress] = useState(0)
  const [savedColors, setSavedColors] = useState(() => {
    // 从 localStorage 加载保存的颜色
    const saved = localStorage.getItem('solid-saved-colors')
    return saved ? JSON.parse(saved) : []
  })

  // 预设纯色方案 (3x3 = 9个)
  const presetColors = [
    { name: '纯白', color: '#ffffff' },
    { name: '暖白', color: '#fff8dc' },
    { name: '冷白', color: '#f0f8ff' },
    { name: '柔和黄', color: '#ffeb3b' },
    { name: '橙光', color: '#ff9800' },
    { name: '红光', color: '#f44336' },
    { name: '蓝光', color: '#2196f3' },
    { name: '绿光', color: '#4caf50' },
    { name: '紫光', color: '#9c27b0' }
  ]

  // 点击屏幕显示/隐藏控制界面
  const handleScreenClick = () => {
    if (showControls) {
      setShowControls(false)
    } else {
      setShowControls(true)
      resetHideTimeout()
    }
  }

  // 重置自动隐藏计时器
  const resetHideTimeout = () => {
    if (hideTimeoutRef.current) {
      clearTimeout(hideTimeoutRef.current)
    }

    hideTimeoutRef.current = setTimeout(() => {
      setShowControls(false)
    }, 5000)
  }

  // 监听控制界面显示状态
  useEffect(() => {
    if (showControls) {
      resetHideTimeout()
      setTimeout(() => {
        if (controlsRef.current) {
          const { scrollHeight, clientHeight } = controlsRef.current
          setShowScrollHint(scrollHeight > clientHeight)
        }
      }, 100)
    } else {
      setShowScrollHint(false)
    }

    return () => {
      if (hideTimeoutRef.current) {
        clearTimeout(hideTimeoutRef.current)
      }
    }
  }, [showControls])

  // 滚动事件处理
  const handleScroll = (e) => {
    const { scrollTop, scrollHeight, clientHeight } = e.target
    const progress = scrollTop / (scrollHeight - clientHeight)
    setScrollProgress(Math.min(Math.max(progress, 0), 1))

    if (showScrollHint && scrollTop > 50) {
      setShowScrollHint(false)
    }
  }

  // 防止移动端页面滚动和缩放
  useEffect(() => {
    document.body.style.overflow = 'hidden'
    document.body.style.position = 'fixed'
    document.body.style.width = '100%'
    document.body.style.height = '100%'

    const preventZoom = (e) => {
      if (e.touches.length > 1) {
        e.preventDefault()
      }
    }

    const preventDoubleClick = (e) => {
      e.preventDefault()
    }

    document.addEventListener('touchstart', preventZoom, { passive: false })
    document.addEventListener('touchmove', preventZoom, { passive: false })
    document.addEventListener('dblclick', preventDoubleClick)

    return () => {
      document.body.style.overflow = ''
      document.body.style.position = ''
      document.body.style.width = ''
      document.body.style.height = ''

      document.removeEventListener('touchstart', preventZoom)
      document.removeEventListener('touchmove', preventZoom)
      document.removeEventListener('dblclick', preventDoubleClick)
    }
  }, [])

  // 颜色切换
  const changeColor = (color) => {
    setIsTransitioning(true)
    setTimeout(() => {
      setCurrentColor(color)
      setIsTransitioning(false)
    }, 150)
    setTimeout(() => {
      setShowControls(false)
    }, 300)
  }

  // 亮度调节
  const handleBrightnessChange = (value) => {
    setBrightness(value)
    resetHideTimeout()
  }

  // 保存颜色
  const saveColor = () => {
    const colorName = `纯色 ${savedColors.length + 1}`
    const newColor = {
      id: Date.now(),
      name: colorName,
      color: currentColor,
      timestamp: new Date().toISOString()
    }

    const updatedColors = [...savedColors, newColor]
    setSavedColors(updatedColors)
    localStorage.setItem('solid-saved-colors', JSON.stringify(updatedColors))
  }

  // 删除保存的颜色
  const deleteSavedColor = (colorId) => {
    const updatedColors = savedColors.filter(color => color.id !== colorId)
    setSavedColors(updatedColors)
    localStorage.setItem('solid-saved-colors', JSON.stringify(updatedColors))
  }

  // 返回主页
  const handleBack = () => {
    navigate('/')
  }

  // 阻止控制面板点击事件冒泡
  const handleControlClick = (e) => {
    e.stopPropagation()
    resetHideTimeout()
  }

  const handleTouchStart = (e) => {
    e.preventDefault()
    handleScreenClick()
  }

  const handleControlTouch = (e) => {
    e.stopPropagation()
    resetHideTimeout()
  }

  return (
    <div
      className={`solid-light ${isTransitioning ? 'transitioning' : ''}`}
      style={{
        backgroundColor: currentColor,
        opacity: brightness / 100
      }}
      onClick={handleScreenClick}
      onTouchStart={handleTouchStart}
    >
      {/* 控制界面 */}
      <div
        ref={controlsRef}
        className={`controls-overlay ${showControls ? 'visible' : ''}`}
        onClick={handleControlClick}
        onTouchStart={handleControlTouch}
        onScroll={handleScroll}
      >
        <div className="controls-content">
          {/* 顶部控制栏 */}
          <div className="top-controls">
            <button className="back-button" onClick={handleBack}>
              <span className="back-icon">←</span>
              返回
            </button>
            <h2 className="mode-title">纯色灯</h2>
            <div className="top-right-controls">
              <div className="brightness-control">
                <span className="brightness-icon">☀️</span>
                <input
                  type="range"
                  min="10"
                  max="100"
                  value={brightness}
                  onChange={(e) => handleBrightnessChange(e.target.value)}
                  className="brightness-slider"
                />
                <span className="brightness-value">{brightness}%</span>
              </div>
              <button className="close-button" onClick={() => setShowControls(false)}>
                <span className="close-icon">×</span>
              </button>
            </div>
          </div>

          {/* 主内容区域 */}
          <div className="main-content">
            {/* 调色盘 */}
            <div className="color-picker-section">
              <h3>自定义颜色</h3>
              <div className="color-picker-container">
                <input
                  type="color"
                  value={currentColor}
                  onChange={(e) => changeColor(e.target.value)}
                  className="color-picker"
                />
                <div className="color-picker-label">
                  <span>选择颜色</span>
                  <span className="color-value">{currentColor.toUpperCase()}</span>
                </div>
                <button
                  className="save-color-btn"
                  onClick={saveColor}
                  disabled={savedColors.length >= 12}
                >
                  💾 保存
                </button>
              </div>
            </div>

            {/* 分栏布局：预设和收藏 */}
            <div className="colors-columns">
              {/* 预设颜色 */}
              <div className="colors-column">
                <h4>预设颜色</h4>
                <div className="preset-colors-grid">
                  {presetColors.map((preset, index) => (
                    <div
                      key={index}
                      className={`preset-color-item ${currentColor === preset.color ? 'active' : ''}`}
                      style={{ backgroundColor: preset.color }}
                      onClick={() => changeColor(preset.color)}
                    >
                      <span className="preset-color-name">{preset.name}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* 收藏的颜色 */}
              <div className="colors-column">
                <h4>我的收藏 ({savedColors.length}/12)</h4>
                {savedColors.length > 0 ? (
                  <div className="saved-colors-grid">
                    {savedColors.map((color) => (
                      <div
                        key={color.id}
                        className={`saved-color-item ${currentColor === color.color ? 'active' : ''}`}
                        style={{ backgroundColor: color.color }}
                        onClick={() => changeColor(color.color)}
                      >
                        <span className="saved-color-name">{color.name}</span>
                        <button
                          className="delete-color-btn"
                          onClick={(e) => {
                            e.stopPropagation()
                            deleteSavedColor(color.id)
                          }}
                        >
                          ×
                        </button>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="empty-saved">
                    <p>暂无收藏的颜色</p>
                    <p>选择颜色后点击保存</p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* 底部区域 */}
          <div className="bottom-section">
            <div className="bottom-hint">
              <p>点击屏幕任意位置隐藏控制界面</p>
            </div>
          </div>
        </div>

        {/* 滚动指示器 */}
        <div className="scroll-indicator">
          <div
            className="scroll-thumb"
            style={{
              height: `${Math.max(scrollProgress * 100, 10)}%`,
              transform: `translateY(${scrollProgress * (100 - Math.max(scrollProgress * 100, 10))}px)`
            }}
          />
        </div>

        {/* 滚动提示 */}
        {showScrollHint && (
          <div className="scroll-hint">
            ↓ 向下滑动查看更多
          </div>
        )}
      </div>
    </div>
  )
}

export default SolidLight
